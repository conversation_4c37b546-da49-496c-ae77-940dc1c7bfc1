
import { Button } from "@/components/ui/button";
import { ArrowRight, MapPin, Mail, Phone } from "lucide-react";
import { Link, useLocation } from "react-router-dom";

const Footer = () => {
  const location = useLocation();
  const isClinicContext = location.pathname.includes('/clinic');
  const isHotelContext = location.pathname.includes('/hotel');

  const getTitle = () => {
    if (isClinicContext) return "Ready to Transform Your Clinic?";
    if (isHotelContext) return "Ready to Transform Your Hotel?";
    return "Ready to Transform Your Business?";
  };

  const getDescription = () => {
    if (isClinicContext) return "Book your free strategy call today and discover how AI can revolutionize your clinic operations.";
    if (isHotelContext) return "Book your free AI strategy session today and discover how we can revolutionize your hotel operations.";
    return "Book your free strategy call today and discover how AI can revolutionize your operations.";
  };

  const getBookCallUrl = () => {
    if (isClinicContext) return "/book-call?industry=clinic";
    if (isHotelContext) return "/book-call?industry=hotel";
    return "/book-call";
  };

  const getBrandName = () => {
    if (isClinicContext) return "Ascent AI Clinic";
    if (isHotelContext) return "Ascent AI Hotel";
    return "Ascent AI";
  };

  const getAboutText = () => {
    if (isClinicContext) return "Your dedicated partner in healthcare innovation, delivering custom AI solutions that transform clinic operations and improve patient care.";
    if (isHotelContext) return "Your dedicated partner in hotel innovation, delivering custom AI solutions that transform operations and enhance guest experiences.";
    return "Your dedicated partner in business innovation, delivering custom AI solutions that transform operations across industries.";
  };

  return (
    <footer className="bg-gray-900 text-white py-20">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          {/* Main CTA Section */}
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold mb-6">
              {getTitle()}
            </h2>

            <p className="text-xl text-gray-300 mb-8">
              {getDescription()}
            </p>

            <Button
              asChild
              size="lg"
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 group"
            >
              <Link to={getBookCallUrl()}>
                {isHotelContext ? "Book Your Free AI Strategy Session" : "Book Your Free Strategy Call"}
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Link>
            </Button>
          </div>

          {/* Footer Content Grid */}
          <div className="border-t border-gray-700 pt-12">
            <div className="grid md:grid-cols-3 gap-8 mb-8">
              {/* Contact Us */}
              <div>
                <h3 className="text-xl font-semibold mb-4">Contact Us</h3>
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <MapPin className="w-5 h-5 text-blue-400 flex-shrink-0" />
                    <span className="text-gray-300">Singapore</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Mail className="w-5 h-5 text-blue-400 flex-shrink-0" />
                    <a
                      href="mailto:<EMAIL>"
                      className="text-gray-300 hover:text-blue-400 transition-colors"
                    >
                      <EMAIL>
                    </a>
                  </div>
                  <div className="flex items-center gap-3">
                    <Phone className="w-5 h-5 text-blue-400 flex-shrink-0" />
                    <a
                      href="tel:+6590032175"
                      className="text-gray-300 hover:text-blue-400 transition-colors"
                    >
                      +65 9003 2175
                    </a>
                  </div>
                </div>
              </div>

              {/* About Ascent AI */}
              <div>
                <h3 className="text-xl font-semibold mb-4">About Ascent AI</h3>
                <p className="text-gray-300 leading-relaxed">
                  {getAboutText()}
                </p>
              </div>

              {/* Quick CTA */}
              <div>
                <h3 className="text-xl font-semibold mb-4">Get Started</h3>
                <p className="text-gray-300 mb-4">
                  Ready to transform your {isClinicContext ? 'clinic' : isHotelContext ? 'hotel' : 'business'} with AI?
                </p>
                <Button asChild className="bg-blue-600 hover:bg-blue-700 w-full">
                  <Link to={getBookCallUrl()}>
                    Book Your Free Session
                  </Link>
                </Button>
              </div>
            </div>

            {/* Bottom Bar */}
            <div className="border-t border-gray-700 pt-8">
              <div className="flex flex-col md:flex-row justify-between items-center">
                <div className="mb-4 md:mb-0">
                  <h3 className="text-2xl font-bold">
                    {getBrandName()}
                  </h3>
                  <p className="text-gray-400">AI Automation Solutions</p>
                </div>

                <div className="text-gray-400 text-sm">
                  © 2025 Ascent AI. All rights reserved.
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
