import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, <PERSON>, Zap, Target, Users, Building2, <PERSON>, <PERSON>rk<PERSON>, T<PERSON>ding<PERSON><PERSON>, <PERSON>, <PERSON>, Rocket } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import Footer from "@/components/Footer";

const GeneralHome = () => {
  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section with WOW Factor */}
      <section className="relative overflow-hidden min-h-screen flex items-center">
        {/* Animated Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900">
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
          {/* Floating Elements */}
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/20 rounded-full mix-blend-multiply filter blur-3xl animate-pulse"></div>
          <div className="absolute top-3/4 right-1/4 w-96 h-96 bg-purple-500/20 rounded-full mix-blend-multiply filter blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-indigo-500/20 rounded-full mix-blend-multiply filter blur-3xl animate-pulse delay-2000"></div>

          {/* Animated Grid */}
          <div className="absolute inset-0 opacity-10">
            <div className="h-full w-full bg-grid-white/[0.05] bg-[size:50px_50px]"></div>
          </div>
        </div>

        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="max-w-6xl mx-auto text-center">
            {/* Main Headline */}
            <div className="mb-8">
              <div className="inline-flex items-center gap-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full px-6 py-3 mb-6">
                <Sparkles className="w-5 h-5 text-yellow-400" />
                <span className="text-white font-medium">The Future of Business Automation</span>
              </div>

              <h1 className="text-5xl sm:text-6xl lg:text-7xl xl:text-8xl font-bold text-white mb-6 leading-tight">
                <span className="bg-gradient-to-r from-blue-400 via-purple-400 to-indigo-400 bg-clip-text text-transparent">
                  Ascent AI
                </span>
                <br />
                <span className="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-light text-white/90">
                  Intelligent Solutions for
                </span>
                <br />
                <span className="bg-gradient-to-r from-yellow-400 via-orange-400 to-red-400 bg-clip-text text-transparent">
                  Every Industry
                </span>
              </h1>
            </div>

            <p className="text-xl sm:text-2xl lg:text-3xl text-white/80 mb-12 leading-relaxed max-w-4xl mx-auto">
              Transform your business with AI-powered automation that
              <span className="text-yellow-400 font-semibold"> reduces costs</span>,
              <span className="text-green-400 font-semibold"> boosts efficiency</span>, and
              <span className="text-blue-400 font-semibold"> drives growth</span>
            </p>

            {/* Industry Selection Cards */}
            <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto mb-12">
              {/* Healthcare Card */}
              <Link to="/clinic" className="group">
                <div className="relative bg-white/10 backdrop-blur-lg border border-white/20 rounded-2xl p-8 hover:bg-white/20 transition-all duration-500 transform hover:scale-105 hover:shadow-2xl">
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-cyan-500/20 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <div className="relative z-10">
                    <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center mb-6 mx-auto group-hover:scale-110 transition-transform duration-300">
                      <Heart className="w-8 h-8 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold text-white mb-4">Healthcare Clinics</h3>
                    <p className="text-white/70 mb-6 leading-relaxed">
                      AI that runs your clinic so you don't have to. Reduce overheads, book more appointments, and eliminate admin chaos.
                    </p>
                    <div className="flex items-center justify-center text-blue-400 font-semibold group-hover:text-blue-300 transition-colors">
                      Explore Healthcare Solutions
                      <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                    </div>
                  </div>
                </div>
              </Link>

              {/* Hotel Card */}
              <Link to="/hotel" className="group">
                <div className="relative bg-white/10 backdrop-blur-lg border border-white/20 rounded-2xl p-8 hover:bg-white/20 transition-all duration-500 transform hover:scale-105 hover:shadow-2xl">
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <div className="relative z-10">
                    <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mb-6 mx-auto group-hover:scale-110 transition-transform duration-300">
                      <Building2 className="w-8 h-8 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold text-white mb-4">Hotels & Hospitality</h3>
                    <p className="text-white/70 mb-6 leading-relaxed">
                      Future-proof your hotel operations with AI. Custom solutions that reduce no-shows, cut costs, and boost efficiency.
                    </p>
                    <div className="flex items-center justify-center text-purple-400 font-semibold group-hover:text-purple-300 transition-colors">
                      Explore Hotel Solutions
                      <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                    </div>
                  </div>
                </div>
              </Link>
            </div>

            {/* Stats Section */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
              <div className="text-center">
                <div className="text-3xl font-bold text-white mb-2">80%</div>
                <div className="text-white/70 text-sm">Admin Time Saved</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white mb-2">2-4</div>
                <div className="text-white/70 text-sm">Weeks to Deploy</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white mb-2">24/7</div>
                <div className="text-white/70 text-sm">AI Support</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white mb-2">100%</div>
                <div className="text-white/70 text-sm">Custom Built</div>
              </div>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white/50 rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </section>

      {/* Why Choose Ascent AI - Stunning Visual Section */}
      <section className="py-20 bg-gradient-to-br from-gray-50 to-blue-50 relative overflow-hidden">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-800 rounded-full px-6 py-3 mb-6">
                <Brain className="w-5 h-5" />
                <span className="font-medium">Powered by Advanced AI</span>
              </div>
              <h2 className="text-4xl sm:text-5xl font-bold text-foreground mb-6">
                Why Choose <span className="text-blue-600">Ascent AI</span>?
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                We build custom AI solutions that automate repetitive tasks, streamline operations, and help your business grow faster than ever before.
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              {/* Custom Solutions */}
              <div className="group">
                <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <Target className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-foreground mb-4">Custom Solutions</h3>
                  <p className="text-muted-foreground leading-relaxed mb-6">
                    Tailored AI systems designed specifically for your business needs. No one-size-fits-all approaches.
                  </p>
                  <div className="flex items-center text-blue-600 font-semibold group-hover:text-blue-700 transition-colors">
                    <span>100% Customized</span>
                    <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </div>
                </div>
              </div>

              {/* Rapid Implementation */}
              <div className="group">
                <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100">
                  <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <Zap className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-foreground mb-4">Rapid Implementation</h3>
                  <p className="text-muted-foreground leading-relaxed mb-6">
                    Get up and running in weeks, not months, with immediate ROI and measurable results.
                  </p>
                  <div className="flex items-center text-green-600 font-semibold group-hover:text-green-700 transition-colors">
                    <span>2-4 Weeks Deploy</span>
                    <Clock className="ml-2 h-4 w-4" />
                  </div>
                </div>
              </div>

              {/* Ongoing Support */}
              <div className="group">
                <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100">
                  <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <Shield className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-foreground mb-4">Ongoing Support</h3>
                  <p className="text-muted-foreground leading-relaxed mb-6">
                    Continuous optimization and support to ensure your success with 24/7 monitoring.
                  </p>
                  <div className="flex items-center text-purple-600 font-semibold group-hover:text-purple-700 transition-colors">
                    <span>24/7 Support</span>
                    <Users className="ml-2 h-4 w-4" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Background Elements */}
        <div className="absolute top-0 right-0 w-96 h-96 bg-blue-200 rounded-full mix-blend-multiply filter blur-3xl opacity-30 animate-pulse"></div>
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-purple-200 rounded-full mix-blend-multiply filter blur-3xl opacity-30 animate-pulse delay-1000"></div>
      </section>

      {/* Results & Impact Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="max-w-6xl mx-auto text-center">
            <h2 className="text-4xl sm:text-5xl font-bold mb-6">
              Transforming Businesses <span className="text-yellow-400">Worldwide</span>
            </h2>
            <p className="text-xl text-white/80 mb-16 max-w-3xl mx-auto">
              Join hundreds of businesses that have already revolutionized their operations with our AI solutions.
            </p>

            <div className="grid md:grid-cols-4 gap-8 mb-16">
              <div className="text-center">
                <div className="w-20 h-20 bg-white/10 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <TrendingUp className="w-10 h-10 text-yellow-400" />
                </div>
                <div className="text-4xl font-bold mb-2">300%</div>
                <div className="text-white/70">Average ROI Increase</div>
              </div>

              <div className="text-center">
                <div className="w-20 h-20 bg-white/10 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <Clock className="w-10 h-10 text-green-400" />
                </div>
                <div className="text-4xl font-bold mb-2">80%</div>
                <div className="text-white/70">Time Saved on Admin</div>
              </div>

              <div className="text-center">
                <div className="w-20 h-20 bg-white/10 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <Users className="w-10 h-10 text-blue-400" />
                </div>
                <div className="text-4xl font-bold mb-2">500+</div>
                <div className="text-white/70">Happy Clients</div>
              </div>

              <div className="text-center">
                <div className="w-20 h-20 bg-white/10 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <Rocket className="w-10 h-10 text-purple-400" />
                </div>
                <div className="text-4xl font-bold mb-2">24/7</div>
                <div className="text-white/70">AI Operations</div>
              </div>
            </div>

            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 max-w-4xl mx-auto">
              <blockquote className="text-2xl font-light italic mb-6">
                "Ascent AI didn't just automate our processes – they transformed our entire business model. We're now operating at a level we never thought possible."
              </blockquote>
              <div className="flex items-center justify-center gap-4">
                <div className="w-12 h-12 bg-gradient-to-br from-yellow-400 to-orange-400 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold">JD</span>
                </div>
                <div className="text-left">
                  <div className="font-semibold">John Doe</div>
                  <div className="text-white/70">CEO, TechCorp</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Animated Background Elements */}
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-white/5 rounded-full animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-white/5 rounded-full animate-pulse delay-1000"></div>
      </section>

      {/* Final CTA Section */}
      <section className="py-20 bg-white relative overflow-hidden">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <div className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 rounded-full px-6 py-3 mb-8">
              <Sparkles className="w-5 h-5" />
              <span className="font-medium">Ready to Transform Your Business?</span>
            </div>

            <h2 className="text-4xl sm:text-5xl font-bold text-foreground mb-6">
              Start Your AI Journey <span className="text-blue-600">Today</span>
            </h2>

            <p className="text-xl text-muted-foreground mb-12 leading-relaxed">
              Book a free strategy call and discover how AI can revolutionize your operations.
              No commitment, just insights tailored to your business.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-12">
              <Link to="/clinic" className="group">
                <Button size="lg" className="bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white px-8 py-4 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                  <Heart className="mr-3 h-6 w-6" />
                  Healthcare Solutions
                  <ArrowRight className="ml-3 h-6 w-6 group-hover:translate-x-1 transition-transform" />
                </Button>
              </Link>

              <Link to="/hotel" className="group">
                <Button size="lg" variant="outline" className="border-2 border-purple-600 text-purple-600 hover:bg-purple-600 hover:text-white px-8 py-4 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                  <Building2 className="mr-3 h-6 w-6" />
                  Hotel Solutions
                  <ArrowRight className="ml-3 h-6 w-6 group-hover:translate-x-1 transition-transform" />
                </Button>
              </Link>
            </div>

            <div className="grid md:grid-cols-3 gap-6 max-w-3xl mx-auto">
              <div className="flex items-center justify-center gap-3 text-muted-foreground">
                <div className="w-8 h-8 bg-green-100 text-green-600 rounded-full flex items-center justify-center">
                  <span className="text-sm font-bold">✓</span>
                </div>
                <span>Free Strategy Call</span>
              </div>

              <div className="flex items-center justify-center gap-3 text-muted-foreground">
                <div className="w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center">
                  <span className="text-sm font-bold">✓</span>
                </div>
                <span>Custom Solution Design</span>
              </div>

              <div className="flex items-center justify-center gap-3 text-muted-foreground">
                <div className="w-8 h-8 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center">
                  <span className="text-sm font-bold">✓</span>
                </div>
                <span>No Long-term Commitment</span>
              </div>
            </div>
          </div>
        </div>

        {/* Background Decoration */}
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
          <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-blue-100 rounded-full mix-blend-multiply filter blur-3xl opacity-50"></div>
          <div className="absolute bottom-1/4 left-1/4 w-96 h-96 bg-purple-100 rounded-full mix-blend-multiply filter blur-3xl opacity-50"></div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default GeneralHome;
