import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON> } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import Footer from "@/components/Footer";

const GeneralHome = () => {
  return (
    <div className="min-h-screen bg-background">
      <section className="relative overflow-hidden bg-gradient-to-br from-blue-50 via-white to-blue-50 py-20 lg:py-32">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-foreground mb-6 leading-tight">
              Ascent AI
              <span className="block text-blue-600">Intelligent Solutions for Every Industry</span>
            </h1>
            
            <p className="text-xl sm:text-2xl text-muted-foreground mb-8 leading-relaxed">
              Transforming businesses with AI-powered automation and intelligent workflows.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/clinic">
                <Button size="lg" className="w-full sm:w-auto">
                  Healthcare Solutions
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </Link>
              <Link to="/hotel">
                <Button size="lg" variant="outline" className="w-full sm:w-auto">
                  Hotel Solutions
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>
      
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Our Industry Solutions</h2>
          
          <div className="grid md:grid-cols-2 gap-8">
            <div className="bg-blue-50 p-6 rounded-lg">
              <h3 className="text-xl font-semibold mb-3">Healthcare Clinics</h3>
              <p className="mb-4">AI that runs your clinic so you don't have to. Reduce overheads, book more appointments, and eliminate admin chaos.</p>
              <Link to="/clinic" className="text-blue-600 hover:underline font-medium">
                Learn more →
              </Link>
            </div>

            <div className="bg-green-50 p-6 rounded-lg">
              <h3 className="text-xl font-semibold mb-3">Hotels & Hospitality</h3>
              <p className="mb-4">Future-proof your hotel operations with AI. Custom solutions that reduce no-shows, cut costs, and boost efficiency.</p>
              <Link to="/hotel" className="text-blue-600 hover:underline font-medium">
                Learn more →
              </Link>
            </div>
          </div>
        </div>
      </section>
      
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-6">Why Choose Ascent AI?</h2>
            <p className="text-lg text-muted-foreground mb-12">
              We build custom AI solutions that automate repetitive tasks, streamline operations, and help your business grow.
            </p>
            
            <div className="grid md:grid-cols-3 gap-6">
              <div className="p-4">
                <h3 className="text-xl font-semibold mb-2">Custom Solutions</h3>
                <p className="text-muted-foreground">Tailored AI systems designed specifically for your business needs.</p>
              </div>
              
              <div className="p-4">
                <h3 className="text-xl font-semibold mb-2">Rapid Implementation</h3>
                <p className="text-muted-foreground">Get up and running in weeks, not months, with immediate ROI.</p>
              </div>
              
              <div className="p-4">
                <h3 className="text-xl font-semibold mb-2">Ongoing Support</h3>
                <p className="text-muted-foreground">Continuous optimization and support to ensure your success.</p>
              </div>
            </div>
          </div>
        </div>
      </section>
      
      <Footer />
    </div>
  );
};

export default GeneralHome;
