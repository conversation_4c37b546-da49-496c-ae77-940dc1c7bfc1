
import HowItWorks from "@/components/HowItWorks";
import Footer from "@/components/Footer";
import { useLocation } from "react-router-dom";
import { Zap, Target, Rocket } from "lucide-react";

const HowItWorksPage = () => {
  const location = useLocation();
  const isClinicContext = location.pathname.includes('/clinic');
  const isHotelContext = location.pathname.includes('/hotel');

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-blue-50 via-white to-blue-50 py-20 lg:py-32">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-foreground mb-6 leading-tight">
              How We Transform
              <span className="block text-blue-600">
                {isClinicContext ? 'Your Clinic' : isHotelContext ? 'Your Hotel' : 'Your Business'}
              </span>
            </h1>

            <p className="text-xl sm:text-2xl text-muted-foreground mb-12 leading-relaxed">
              {isClinicContext
                ? "Our proven process for implementing AI solutions that reduce overheads and eliminate admin chaos."
                : isHotelContext
                ? "Our proven process for implementing AI solutions that reduce no-shows, cut costs, and boost efficiency."
                : "Our proven process for implementing AI solutions that deliver real results and measurable ROI."}
            </p>

            {/* Key Benefits */}
            <div className="grid md:grid-cols-3 gap-6 max-w-3xl mx-auto">
              <div className="flex items-center justify-center gap-3 bg-white p-4 rounded-lg shadow-sm">
                <Zap className="w-6 h-6 text-blue-600" />
                <span className="font-semibold text-foreground">Fast Implementation</span>
              </div>
              <div className="flex items-center justify-center gap-3 bg-white p-4 rounded-lg shadow-sm">
                <Target className="w-6 h-6 text-green-600" />
                <span className="font-semibold text-foreground">Tailored Solutions</span>
              </div>
              <div className="flex items-center justify-center gap-3 bg-white p-4 rounded-lg shadow-sm">
                <Rocket className="w-6 h-6 text-purple-600" />
                <span className="font-semibold text-foreground">Proven Results</span>
              </div>
            </div>
          </div>
        </div>

        {/* Background decoration */}
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse"></div>
          <div className="absolute top-3/4 right-1/4 w-64 h-64 bg-indigo-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse delay-1000"></div>
        </div>
      </section>

      <HowItWorks />

      <Footer />
    </div>
  );
};

export default HowItWorksPage;
