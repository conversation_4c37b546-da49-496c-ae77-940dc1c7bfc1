
import { useLocation } from "react-router-dom";
import Footer from "@/components/Footer";
import { Brain, Users, Zap, Target, Globe, Award } from "lucide-react";

const AboutPage = () => {
  const location = useLocation();
  const isClinicContext = location.pathname.includes('/clinic');
  const isHotelContext = location.pathname.includes('/hotel');

  const getIndustryText = () => {
    if (isClinicContext) return "healthcare";
    if (isHotelContext) return "hospitality";
    return "business";
  };

  const getServiceText = () => {
    if (isClinicContext) return "care";
    if (isHotelContext) return "guest experiences";
    return "service";
  };

  const getWorkflowText = () => {
    if (isClinicContext) return "clinic's";
    if (isHotelContext) return "hotel's";
    return "business's";
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-blue-50 via-white to-blue-50 py-20 lg:py-32">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-foreground mb-6 leading-tight">
              About
              <span className="block text-blue-600">Ascent AI</span>
            </h1>

            <p className="text-xl sm:text-2xl text-muted-foreground mb-8 leading-relaxed">
              We're on a mission to transform {getIndustryText()} through intelligent automation and custom AI solutions.
            </p>

            <div className="flex items-center justify-center gap-2 text-blue-600">
              <Brain className="w-6 h-6" />
              <span className="font-semibold">AI-Powered Innovation</span>
            </div>
          </div>
        </div>

        {/* Background decoration */}
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse"></div>
          <div className="absolute top-3/4 right-1/4 w-64 h-64 bg-indigo-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse delay-1000"></div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <div className="grid md:grid-cols-2 gap-12 items-center mb-16">
              <div>
                <h2 className="text-3xl font-bold text-foreground mb-6">
                  Who We Are
                </h2>
                <div className="space-y-6 text-lg text-muted-foreground leading-relaxed">
                  <p>
                    At <strong className="text-foreground">Ascent AI</strong>, we help businesses
                    {isClinicContext ? " in the healthcare sector" : isHotelContext ? " in the hospitality industry" : ""} work smarter, reduce costs,
                    and grow faster through custom-built AI solutions.
                  </p>

                  <p>
                    We are a newly founded team of highly skilled full-stack AI developers who can create
                    anything you envision. Whether you need to automate repetitive admin tasks, streamline
                    communications, or increase efficiency, our team can build it with precision and speed.
                  </p>

                  <p className="font-semibold text-blue-600">
                    If you can think it, we can build it.
                  </p>
                </div>
              </div>

              <div className="bg-gradient-to-br from-blue-50 to-indigo-100 p-8 rounded-xl">
                <div className="grid grid-cols-2 gap-6">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-3">
                      <Users className="w-8 h-8" />
                    </div>
                    <h3 className="font-semibold text-foreground">Expert Team</h3>
                    <p className="text-sm text-muted-foreground">Full-stack AI developers</p>
                  </div>

                  <div className="text-center">
                    <div className="w-16 h-16 bg-green-600 text-white rounded-full flex items-center justify-center mx-auto mb-3">
                      <Zap className="w-8 h-8" />
                    </div>
                    <h3 className="font-semibold text-foreground">Fast Delivery</h3>
                    <p className="text-sm text-muted-foreground">Rapid implementation</p>
                  </div>

                  <div className="text-center">
                    <div className="w-16 h-16 bg-purple-600 text-white rounded-full flex items-center justify-center mx-auto mb-3">
                      <Target className="w-8 h-8" />
                    </div>
                    <h3 className="font-semibold text-foreground">Custom Solutions</h3>
                    <p className="text-sm text-muted-foreground">Tailored to your needs</p>
                  </div>

                  <div className="text-center">
                    <div className="w-16 h-16 bg-orange-600 text-white rounded-full flex items-center justify-center mx-auto mb-3">
                      <Award className="w-8 h-8" />
                    </div>
                    <h3 className="font-semibold text-foreground">Proven Results</h3>
                    <p className="text-sm text-muted-foreground">Measurable ROI</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Mission */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-foreground mb-8">
              Our Mission
            </h2>

            <div className="bg-white p-8 rounded-xl shadow-lg">
              <div className="space-y-6 text-lg text-muted-foreground leading-relaxed">
                <p>
                  Our focus is on solving the real problems business owners face daily, from high overhead
                  costs and inefficient workflows to missed revenue opportunities. We specialise in crafting
                  intelligent systems that automate the routine, improve operational efficiency, and free
                  your team to focus on delivering better {getServiceText()}.
                </p>

                <p>
                  What sets us apart is not just our technical expertise, but our hands-on, collaborative
                  approach. We guide you from concept to execution, building tools that integrate seamlessly
                  into your {getWorkflowText()} workflow and deliver measurable results.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Global Presence */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-foreground mb-12">
              Global Presence
            </h2>

            <div className="grid md:grid-cols-2 gap-8">
              <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-8 rounded-xl">
                <div className="flex items-center justify-center mb-4">
                  <div className="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center">
                    <Globe className="w-8 h-8" />
                  </div>
                </div>
                <h3 className="text-xl font-bold text-foreground mb-2">Singapore</h3>
                <p className="text-muted-foreground mb-4">
                  Our Asia-Pacific headquarters, serving the growing tech ecosystem in Southeast Asia.
                </p>
                <div className="text-sm text-blue-600">
                  <p><EMAIL></p>
                  <p>+65 9003 2175</p>
                </div>
              </div>

              <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-8 rounded-xl">
                <div className="flex items-center justify-center mb-4">
                  <div className="w-16 h-16 bg-purple-600 text-white rounded-full flex items-center justify-center">
                    <Globe className="w-8 h-8" />
                  </div>
                </div>
                <h3 className="text-xl font-bold text-foreground mb-2">Paris</h3>
                <p className="text-muted-foreground mb-4">
                  Our European operations center, bringing AI innovation to businesses across Europe.
                </p>
                <div className="text-sm text-purple-600">
                  <p><EMAIL></p>
                  <p>+33 7 67 19 99 19</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default AboutPage;
