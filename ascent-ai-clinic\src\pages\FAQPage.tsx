
import { useLocation } from "react-router-dom";
import Footer from "@/components/Footer";
import { HelpCircle, Shield, Users, Clock, Wrench, DollarSign, Laptop, Phone } from "lucide-react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

const FAQPage = () => {
  const location = useLocation();
  const isClinicContext = location.pathname.includes('/clinic');
  const isHotelContext = location.pathname.includes('/hotel');

  // General FAQs for all industries
  const generalFaqs = [
    {
      question: "Is it safe to use AI in my business?",
      answer: "Yes. We fully comply with all relevant regulations and ensure all data is kept secure and confidential.",
      icon: Shield,
      category: "Security"
    },
    {
      question: "Will this replace my existing staff?",
      answer: "No. Our goal is to empower your team, not replace them. By automating repetitive tasks, your staff can focus on higher-value activities, improving efficiency and morale.",
      icon: Users,
      category: "Team"
    },
    {
      question: "How quickly can we get started?",
      answer: "We can begin with a free strategy call first. Most implementations go live within 2 to 4 weeks, depending on the complexity of the solution needed.",
      icon: Clock,
      category: "Timeline"
    },
    {
      question: "What kind of tasks can you automate?",
      answer: "We can automate data entry, form filling, internal task assignments, customer communications, and responses to common inquiries through AI chat agents or phone bots.",
      icon: Wrench,
      category: "Capabilities"
    },
    {
      question: "Do you integrate with existing systems?",
      answer: "Yes. During onboarding, we assess your current systems and build solutions that seamlessly integrate with your existing platforms and tools.",
      icon: Laptop,
      category: "Integration"
    },
    {
      question: "What happens if something breaks or needs support?",
      answer: "We provide ongoing support and maintenance. If anything goes wrong or needs adjusting, our team is on standby to fix issues quickly and keep everything running smoothly.",
      icon: Phone,
      category: "Support"
    },
    {
      question: "How much does it cost?",
      answer: "Pricing depends on the complexity of the solution and your business's needs. We offer flexible packages designed to deliver a return on investment through cost savings, time efficiency, and increased revenue.",
      icon: DollarSign,
      category: "Pricing"
    },
    {
      question: "Do I need to be tech-savvy to use this?",
      answer: "Not at all. Our development team handles all the technical work for you, so you don't have to learn anything yourself.",
      icon: HelpCircle,
      category: "Ease of Use"
    }
  ];
  
  // Healthcare-specific FAQs
  const clinicFaqs = [
    {
      question: "Is it safe to use AI in a clinic setting?",
      answer: "Yes. We fully comply with healthcare regulations and ensure all patient data is kept secure and confidential.",
      icon: Shield,
      category: "Security"
    },
    {
      question: "Will this replace my existing staff?",
      answer: "No. Our goal is to empower your team, not replace them. By automating repetitive tasks, your staff can focus on higher-value activities like patient care, improving efficiency and morale.",
      icon: Users,
      category: "Team"
    },
    {
      question: "How quickly can we get started?",
      answer: "We can begin with a free strategy call first. Most clinics go live within 2 to 4 weeks, depending on the complexity of the solution needed.",
      icon: Clock,
      category: "Timeline"
    },
    {
      question: "What kind of tasks can you automate?",
      answer: "We can automate appointment booking, patient follow-ups, reminders, data entry, form filling, internal task assignments, and even responses to common inquiries through AI chat agents or phone bots.",
      icon: Wrench,
      category: "Capabilities"
    },
    {
      question: "Do you integrate with our existing clinic systems?",
      answer: "Yes. During onboarding, we assess your current systems and build solutions that seamlessly integrate with your electronic medical records (EMR) platforms, including Plato, as well as your scheduling tools.",
      icon: Laptop,
      category: "Integration"
    },
    {
      question: "What happens if something breaks or needs support?",
      answer: "We provide ongoing support and maintenance. If anything goes wrong or needs adjusting, our team is on standby to fix issues quickly and keep everything running smoothly.",
      icon: Phone,
      category: "Support"
    },
    {
      question: "How much does it cost?",
      answer: "Pricing depends on the complexity of the solution and your clinic's needs. We offer flexible packages designed to deliver a return on investment through cost savings, time efficiency, and increased revenue.",
      icon: DollarSign,
      category: "Pricing"
    },
    {
      question: "Do I need to be tech-savvy to use this?",
      answer: "Not at all. Our development team handles all the technical work for you, so you don't have to learn anything yourself.",
      icon: HelpCircle,
      category: "Ease of Use"
    },
    {
      question: "Can this work for multi-location clinics?",
      answer: "Yes. Our AI solutions are scalable and can support both single-location and multi-site operations with central oversight and reporting.",
      icon: Users,
      category: "Scalability"
    }
  ];

  // Hotel-specific FAQs
  const hotelFaqs = [
    {
      question: "Is AI hard to implement in my hotel?",
      answer: "Not at all. We do all the heavy lifting, from integration to training your staff.",
      icon: HelpCircle,
      category: "Implementation"
    },
    {
      question: "Do I need to hire new IT staff?",
      answer: "No. Our systems are designed to be user-friendly and require minimal technical maintenance.",
      icon: Users,
      category: "Staffing"
    },
    {
      question: "Can your AI work with our existing hotel management system (PMS)?",
      answer: "Yes. We integrate seamlessly with most modern PMS and CRM platforms.",
      icon: Laptop,
      category: "Integration"
    },
    {
      question: "Is it expensive?",
      answer: "Our solutions are priced based on outcomes and ROI. Most clients see cost savings within the first few months.",
      icon: DollarSign,
      category: "Pricing"
    },
    {
      question: "How quickly can we see results?",
      answer: "Most hotels start seeing improvements within 2-4 weeks of implementation, with full benefits realized within 2-3 months.",
      icon: Clock,
      category: "Timeline"
    },
    {
      question: "Will this disrupt our current operations?",
      answer: "No. We design our solutions to integrate smoothly with your existing workflows without disrupting daily operations.",
      icon: Shield,
      category: "Operations"
    }
  ];

  const getFaqs = () => {
    if (isClinicContext) return clinicFaqs;
    if (isHotelContext) return hotelFaqs;
    return generalFaqs;
  };

  const faqs = getFaqs();

  const getContextTitle = () => {
    if (isClinicContext) return "Clinic AI Solutions";
    if (isHotelContext) return "Hotel AI Solutions";
    return "AI Solutions";
  };

  const getContextDescription = () => {
    if (isClinicContext) return "Find answers to common questions about implementing AI in your healthcare clinic.";
    if (isHotelContext) return "Find answers to common questions about implementing AI in your hotel operations.";
    return "Find answers to common questions about our AI solutions.";
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-blue-50 via-white to-blue-50 py-20 lg:py-32">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-foreground mb-6 leading-tight">
              Frequently Asked
              <span className="block text-blue-600">Questions</span>
            </h1>

            <p className="text-xl sm:text-2xl text-muted-foreground mb-8 leading-relaxed">
              {getContextDescription()}
            </p>

            <div className="flex items-center justify-center gap-2 text-blue-600">
              <HelpCircle className="w-6 h-6" />
              <span className="font-semibold">{getContextTitle()}</span>
            </div>
          </div>
        </div>

        {/* Background decoration */}
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse"></div>
          <div className="absolute top-3/4 right-1/4 w-64 h-64 bg-indigo-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse delay-1000"></div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <Accordion type="single" collapsible className="space-y-6">
              {faqs.map((faq, index) => {
                const IconComponent = faq.icon;
                return (
                  <AccordionItem
                    key={index}
                    value={`item-${index}`}
                    className="bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-all duration-300"
                  >
                    <AccordionTrigger className="text-left font-medium text-foreground hover:no-underline py-6 px-6">
                      <div className="flex items-center gap-4">
                        <div className="w-10 h-10 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                          <IconComponent className="w-5 h-5" />
                        </div>
                        <div className="text-left">
                          <div className="text-sm text-blue-600 font-medium mb-1">{faq.category}</div>
                          <div className="text-lg font-semibold">{faq.question}</div>
                        </div>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent className="text-muted-foreground leading-relaxed pb-6 px-6">
                      <div className="ml-14">
                        {faq.answer}
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                );
              })}
            </Accordion>

            {/* Contact CTA */}
            <div className="mt-16 text-center">
              <div className="bg-gradient-to-br from-blue-50 to-indigo-100 p-8 rounded-xl">
                <h3 className="text-2xl font-bold text-foreground mb-4">
                  Still Have Questions?
                </h3>
                <p className="text-muted-foreground mb-6">
                  Can't find the answer you're looking for? We'd love to chat about your specific needs.
                </p>
                <a
                  href={`/book-call${isClinicContext ? '?industry=clinic' : isHotelContext ? '?industry=hotel' : ''}`}
                  className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Book a Free Call
                  <Phone className="ml-2 h-4 w-4" />
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default FAQPage;
