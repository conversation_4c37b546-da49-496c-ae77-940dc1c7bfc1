
import { ArrowRight, Search, Wrench, Bar<PERSON>hart3, <PERSON>, Users } from "lucide-react";
import { useLocation } from "react-router-dom";

const HowItWorks = () => {
  const location = useLocation();
  const isClinicContext = location.pathname.includes('/clinic');
  const isHotelContext = location.pathname.includes('/hotel');

  const getSteps = () => {
    if (isHotelContext) {
      return [
        {
          number: "1",
          title: "Discover",
          icon: Search,
          description: "We start with a deep dive into your hotel's current operations and pain points. This isn't a one-size-fits-all approach; we design around your unique needs.",
          color: "bg-blue-600"
        },
        {
          number: "2",
          title: "Prototype",
          icon: Wrench,
          description: "We craft tailored AI workflows and interface mockups to give you a clear vision of how your solution will function.",
          color: "bg-green-600"
        },
        {
          number: "3",
          title: "Develop",
          icon: Rocket,
          description: "Our team of expert engineers builds, tests, and refines the tool until it's ready for seamless integration into your daily operations.",
          color: "bg-purple-600"
        },
        {
          number: "4",
          title: "Deploy",
          icon: Users,
          description: "We handle deployment and provide your staff with the training needed to use the new system with ease.",
          color: "bg-orange-600"
        },
        {
          number: "5",
          title: "Scale",
          icon: BarChart3,
          description: "We stay with you post-launch, improving, scaling, and adapting your AI solution as your business grows.",
          color: "bg-red-600"
        }
      ];
    }

    return [
      {
        number: "1",
        title: "Research",
        icon: Search,
        description: isClinicContext
          ? "Free strategy call where we dive into your clinic workflow bottlenecks and identify automation opportunities."
          : "Free strategy call where we dive into your workflow bottlenecks and identify automation opportunities.",
        color: "bg-blue-600"
      },
      {
        number: "2",
        title: "Build",
        icon: Wrench,
        description: "Our team builds tailored AI solutions that solve your specific pain points with precision and efficiency.",
        color: "bg-green-600"
      },
      {
        number: "3",
        title: "Analyse",
        icon: BarChart3,
        description: "Monthly check-ins to ensure real-world effectiveness, optimize performance, and scale your solutions.",
        color: "bg-purple-600"
      }
    ];
  };

  const steps = getSteps();

  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-foreground mb-6">
              {isClinicContext
                ? "Here's How We Transform Your Clinic in 30 Days"
                : isHotelContext
                ? "Our Proven 5-Step Process"
                : "Here's How We Transform Your Business in 30 Days"}
            </h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              {isHotelContext
                ? "From discovery to deployment, we guide you through every step of your AI transformation journey."
                : "Our streamlined approach ensures rapid implementation and measurable results."}
            </p>
          </div>

          <div className={`grid grid-cols-1 ${steps.length === 5 ? 'lg:grid-cols-5 md:grid-cols-3' : 'md:grid-cols-3'} gap-8`}>
            {steps.map((step, index) => {
              const IconComponent = step.icon;
              return (
                <div key={index} className="relative">
                  <div className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 h-full">
                    <div className="flex flex-col items-center text-center">
                      <div className={`w-16 h-16 ${step.color} text-white rounded-full flex items-center justify-center mb-4 shadow-lg`}>
                        <IconComponent className="w-8 h-8" />
                      </div>
                      <div className="text-sm font-semibold text-gray-500 mb-2">
                        STEP {step.number}
                      </div>
                      <h3 className="text-xl font-bold text-foreground mb-4">
                        {step.title}
                      </h3>
                      <p className="text-muted-foreground leading-relaxed text-sm">
                        {step.description}
                      </p>
                    </div>
                  </div>

                  {/* Arrow connector for desktop */}
                  {index < steps.length - 1 && (
                    <div className="hidden lg:block absolute top-1/2 -right-4 transform -translate-y-1/2 z-10">
                      <div className="bg-white rounded-full p-2 shadow-md">
                        <ArrowRight className="text-blue-600 h-5 w-5" />
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>

          {/* Bottom CTA */}
          <div className="text-center mt-16">
            <div className="bg-white rounded-xl p-8 shadow-lg max-w-2xl mx-auto">
              <h3 className="text-2xl font-bold text-foreground mb-4">
                Ready to Get Started?
              </h3>
              <p className="text-muted-foreground mb-6">
                Book your free strategy call today and see how we can transform your {isClinicContext ? 'clinic' : isHotelContext ? 'hotel' : 'business'}.
              </p>
              <a
                href={`/book-call${isClinicContext ? '?industry=clinic' : isHotelContext ? '?industry=hotel' : ''}`}
                className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors"
              >
                Book Your Free Call
                <ArrowRight className="ml-2 h-4 w-4" />
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HowItWorks;
